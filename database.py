"""
Database models and operations for Prometheus AI
"""
import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from config import settings
from utils import setup_logging

logger = setup_logging()


class Database:
    """Simple SQLite database manager"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or "prometheus_ai.db"
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialize database tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Strategy blueprints table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_blueprints (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    topic TEXT NOT NULL,
                    blueprint_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Generated content table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS generated_content (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    blueprint_id INTEGER,
                    content_type TEXT NOT NULL,
                    title TEXT,
                    content TEXT,
                    metadata TEXT,
                    status TEXT DEFAULT 'draft',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (blueprint_id) REFERENCES strategy_blueprints (id)
                )
            ''')
            
            # SEO analysis table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS seo_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT NOT NULL,
                    analysis_data TEXT NOT NULL,
                    score INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Social media posts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS social_posts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    platform TEXT NOT NULL,
                    content TEXT NOT NULL,
                    media_url TEXT,
                    scheduled_time TIMESTAMP,
                    posted_time TIMESTAMP,
                    status TEXT DEFAULT 'scheduled',
                    engagement_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Email campaigns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS email_campaigns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    campaign_name TEXT NOT NULL,
                    subject TEXT NOT NULL,
                    content TEXT NOT NULL,
                    recipient_list TEXT,
                    sent_count INTEGER DEFAULT 0,
                    open_count INTEGER DEFAULT 0,
                    click_count INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'draft',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Analytics data table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analytics_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    date_recorded DATE NOT NULL,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Workflow executions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS workflow_executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    workflow_name TEXT NOT NULL,
                    status TEXT NOT NULL,
                    input_data TEXT,
                    output_data TEXT,
                    error_message TEXT,
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP
                )
            ''')
            
            conn.commit()
            logger.info("Database initialized successfully")
    
    def save_blueprint(self, topic: str, blueprint_data: Dict) -> int:
        """Save strategy blueprint"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO strategy_blueprints (topic, blueprint_data)
                VALUES (?, ?)
            ''', (topic, json.dumps(blueprint_data)))
            conn.commit()
            return cursor.lastrowid
    
    def get_blueprint(self, blueprint_id: int) -> Optional[Dict]:
        """Get strategy blueprint by ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM strategy_blueprints WHERE id = ?
            ''', (blueprint_id,))
            row = cursor.fetchone()
            
            if row:
                return {
                    'id': row[0],
                    'topic': row[1],
                    'blueprint_data': json.loads(row[2]),
                    'created_at': row[3],
                    'updated_at': row[4]
                }
            return None
    
    def save_content(self, blueprint_id: int, content_type: str, title: str, 
                    content: str, metadata: Dict = None) -> int:
        """Save generated content"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO generated_content 
                (blueprint_id, content_type, title, content, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (blueprint_id, content_type, title, content, 
                  json.dumps(metadata) if metadata else None))
            conn.commit()
            return cursor.lastrowid
    
    def get_content(self, content_id: int) -> Optional[Dict]:
        """Get generated content by ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM generated_content WHERE id = ?
            ''', (content_id,))
            row = cursor.fetchone()
            
            if row:
                return {
                    'id': row[0],
                    'blueprint_id': row[1],
                    'content_type': row[2],
                    'title': row[3],
                    'content': row[4],
                    'metadata': json.loads(row[5]) if row[5] else None,
                    'status': row[6],
                    'created_at': row[7]
                }
            return None
    
    def save_analytics(self, source: str, metric_name: str, metric_value: float, 
                      date_recorded: str, metadata: Dict = None):
        """Save analytics data"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO analytics_data 
                (source, metric_name, metric_value, date_recorded, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (source, metric_name, metric_value, date_recorded,
                  json.dumps(metadata) if metadata else None))
            conn.commit()
    
    def get_recent_analytics(self, source: str, days: int = 30) -> List[Dict]:
        """Get recent analytics data"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM analytics_data 
                WHERE source = ? AND date_recorded >= date('now', '-{} days')
                ORDER BY date_recorded DESC
            '''.format(days), (source,))
            
            rows = cursor.fetchall()
            return [{
                'id': row[0],
                'source': row[1],
                'metric_name': row[2],
                'metric_value': row[3],
                'date_recorded': row[4],
                'metadata': json.loads(row[5]) if row[5] else None,
                'created_at': row[6]
            } for row in rows]


# Global database instance
db = Database()
