"""
Test script for Module 2: Content Generation Module
"""
import asyncio
import sys
import os
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.content_generation import ContentGenerationModule
from utils import setup_logging, load_json

logger = setup_logging()


async def test_content_generation():
    """Test the Content Generation Module"""
    print("✍️ Testing Content Generation Module")
    print("=" * 50)
    
    # Initialize the module
    generator = ContentGenerationModule()
    
    # Load a sample blueprint (or create one)
    sample_blueprint = {
        "suggested_blog_title": "The Complete Guide to Sustainable Gardening in 2024",
        "target_audience": "Beginners and intermediates interested in sustainable gardening",
        "key_talking_points": [
            "What is sustainable gardening and why is it important?",
            "Getting started with sustainable gardening",
            "Common sustainable gardening mistakes to avoid",
            "Best practices for sustainable gardening",
            "Tools and resources for sustainable gardening",
            "Future trends in sustainable gardening"
        ],
        "seo_keywords": [
            "sustainable gardening",
            "eco-friendly gardening",
            "organic gardening",
            "green gardening",
            "sustainable garden",
            "environmentally friendly gardening",
            "sustainable landscaping",
            "green thumb",
            "organic vegetables",
            "composting"
        ],
        "content_angle": "Comprehensive beginner-friendly guide",
        "call_to_action": "Download our free sustainable gardening checklist",
        "content_format": "Long-form blog post with infographics",
        "distribution_channels": ["Blog", "Social Media", "Email Newsletter", "SEO"]
    }
    
    print(f"Generating content for: {sample_blueprint['suggested_blog_title']}")
    print("-" * 50)
    
    try:
        # Generate all content assets
        content_assets = await generator.generate_content(sample_blueprint)
        
        print("✅ Content generation completed successfully!")
        print(f"\n📊 Generated Assets ({len(content_assets)}):")
        
        for asset_type, asset_data in content_assets.items():
            print(f"\n🔹 {asset_type.upper().replace('_', ' ')}")
            
            if asset_type == 'blog_post':
                print(f"  • Title: {asset_data.get('title', 'N/A')}")
                print(f"  • Word Count: {asset_data.get('word_count', 'N/A')}")
                print(f"  • File: {asset_data.get('filename', 'N/A')}")
                print(f"  • Keywords Used: {len(asset_data.get('keywords_used', []))}")
                
            elif asset_type == 'social_posts':
                posts = asset_data.get('posts', {})
                print(f"  • Platforms: {len(posts)}")
                for platform, post in posts.items():
                    print(f"    - {platform.title()}: {post[:50]}{'...' if len(post) > 50 else ''}")
                    
            elif asset_type == 'email_content':
                print(f"  • Subject Lines: {len(asset_data.get('subject_lines', []))}")
                for i, subject in enumerate(asset_data.get('subject_lines', [])[:3], 1):
                    print(f"    {i}. {subject}")
                    
            elif asset_type == 'featured_image':
                print(f"  • File: {asset_data.get('filename', 'N/A')}")
                print(f"  • Dimensions: {asset_data.get('dimensions', 'N/A')}")
                
            elif asset_type == 'seo_elements':
                print(f"  • Meta Title: {asset_data.get('meta_title', 'N/A')[:50]}...")
                print(f"  • Meta Description: {asset_data.get('meta_description', 'N/A')[:50]}...")
                print(f"  • Keywords: {asset_data.get('meta_keywords', 'N/A')[:50]}...")
        
        # Show file locations
        print(f"\n📁 Generated Files:")
        if 'blog_post' in content_assets:
            blog_file = content_assets['blog_post'].get('filepath', 'N/A')
            print(f"  • Blog Post: {blog_file}")
            
        if 'featured_image' in content_assets:
            image_file = content_assets['featured_image'].get('filepath', 'N/A')
            print(f"  • Featured Image: {image_file}")
        
        print(f"\n💡 Usage Tips:")
        print(f"  • Open the HTML file in your browser to preview the blog post")
        print(f"  • Use the social media posts for immediate distribution")
        print(f"  • The email content is ready for your newsletter")
        print(f"  • SEO elements can be added to your CMS")
        
        print("\n" + "=" * 50)
        print("✅ Module 2 test completed successfully!")
        
        return content_assets
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        logger.error(f"Content generation test failed: {str(e)}")
        return None


if __name__ == "__main__":
    print("🚀 Prometheus AI - Module 2 Test")
    print("Testing Content Generation Module...")
    print()
    
    # Run the test
    result = asyncio.run(test_content_generation())
    
    if result:
        print("\n📁 Check the 'output' directory for all generated files.")
        print("🌐 Open the HTML file in your browser to see the blog post!")
    else:
        print("\n❌ Test failed. Check logs for details.")
