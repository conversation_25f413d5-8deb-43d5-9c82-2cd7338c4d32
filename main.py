"""
Prometheus AI - Main Application Entry Point
"""
import asyncio
import sys
from pathlib import Path
from typing import Dict, Any

from config import settings
from utils import setup_logging
from database import db

# Import modules (will be created)
from modules.intelligence import MarketIntelligenceEngine
from modules.content_generation import ContentGenerationModule
from modules.seo_engine import SEOEngine
from modules.orchestrator import AutomationOrchestrator

logger = setup_logging()


class PrometheusAI:
    """Main Prometheus AI application class"""
    
    def __init__(self):
        self.intelligence_engine = MarketIntelligenceEngine()
        self.content_generator = ContentGenerationModule()
        self.seo_engine = SEOEngine()
        self.orchestrator = AutomationOrchestrator()
        
        logger.info("Prometheus AI initialized successfully")
    
    async def run_full_campaign(self, topic: str, goal: str = "increase leads") -> Dict[str, Any]:
        """
        Run a complete marketing campaign for a given topic
        
        Args:
            topic: The main topic/keyword to focus on
            goal: The marketing goal (default: "increase leads")
        
        Returns:
            Dict containing all generated assets and analytics
        """
        logger.info(f"Starting full campaign for topic: {topic}")
        
        try:
            # Step 1: Generate strategy blueprint
            logger.info("Step 1: Generating market intelligence blueprint...")
            blueprint = await self.intelligence_engine.generate_blueprint(topic)
            
            if not blueprint:
                raise Exception("Failed to generate strategy blueprint")
            
            # Save blueprint to database
            blueprint_id = db.save_blueprint(topic, blueprint)
            logger.info(f"Blueprint saved with ID: {blueprint_id}")
            
            # Step 2: Generate content
            logger.info("Step 2: Generating content assets...")
            content_assets = await self.content_generator.generate_content(blueprint)
            
            # Save content to database
            for asset_type, asset_data in content_assets.items():
                db.save_content(
                    blueprint_id=blueprint_id,
                    content_type=asset_type,
                    title=asset_data.get('title', ''),
                    content=asset_data.get('content', ''),
                    metadata=asset_data.get('metadata', {})
                )
            
            # Step 3: SEO optimization
            logger.info("Step 3: Running SEO optimization...")
            seo_results = await self.seo_engine.optimize_content(content_assets, blueprint)
            
            # Step 4: Schedule and execute promotion
            logger.info("Step 4: Orchestrating multi-channel promotion...")
            promotion_results = await self.orchestrator.execute_promotion_workflow(
                blueprint, content_assets, seo_results
            )
            
            # Compile final results
            campaign_results = {
                'blueprint_id': blueprint_id,
                'topic': topic,
                'goal': goal,
                'blueprint': blueprint,
                'content_assets': content_assets,
                'seo_results': seo_results,
                'promotion_results': promotion_results,
                'status': 'completed'
            }
            
            logger.info("Full campaign completed successfully")
            return campaign_results
            
        except Exception as e:
            logger.error(f"Campaign failed: {str(e)}")
            return {
                'topic': topic,
                'goal': goal,
                'status': 'failed',
                'error': str(e)
            }
    
    async def run_intelligence_only(self, topic: str) -> Dict[str, Any]:
        """Run only the market intelligence analysis"""
        logger.info(f"Running intelligence analysis for: {topic}")
        
        try:
            blueprint = await self.intelligence_engine.generate_blueprint(topic)
            blueprint_id = db.save_blueprint(topic, blueprint)
            
            return {
                'blueprint_id': blueprint_id,
                'topic': topic,
                'blueprint': blueprint,
                'status': 'completed'
            }
        except Exception as e:
            logger.error(f"Intelligence analysis failed: {str(e)}")
            return {
                'topic': topic,
                'status': 'failed',
                'error': str(e)
            }
    
    async def run_content_generation(self, blueprint_id: int) -> Dict[str, Any]:
        """Generate content from existing blueprint"""
        logger.info(f"Generating content for blueprint ID: {blueprint_id}")
        
        try:
            blueprint_data = db.get_blueprint(blueprint_id)
            if not blueprint_data:
                raise Exception(f"Blueprint {blueprint_id} not found")
            
            blueprint = blueprint_data['blueprint_data']
            content_assets = await self.content_generator.generate_content(blueprint)
            
            # Save content to database
            for asset_type, asset_data in content_assets.items():
                db.save_content(
                    blueprint_id=blueprint_id,
                    content_type=asset_type,
                    title=asset_data.get('title', ''),
                    content=asset_data.get('content', ''),
                    metadata=asset_data.get('metadata', {})
                )
            
            return {
                'blueprint_id': blueprint_id,
                'content_assets': content_assets,
                'status': 'completed'
            }
        except Exception as e:
            logger.error(f"Content generation failed: {str(e)}")
            return {
                'blueprint_id': blueprint_id,
                'status': 'failed',
                'error': str(e)
            }


async def main():
    """Main entry point"""
    print("🚀 Welcome to Prometheus AI - The Autonomous Digital Growth Engine")
    print("=" * 70)
    
    # Initialize the application
    app = PrometheusAI()
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python main.py <topic> [goal]")
        print("  python main.py 'sustainable gardening' 'increase leads'")
        print("  python main.py 'AI automation' 'brand awareness'")
        return
    
    topic = sys.argv[1]
    goal = sys.argv[2] if len(sys.argv) > 2 else "increase leads"
    
    print(f"Topic: {topic}")
    print(f"Goal: {goal}")
    print("-" * 70)
    
    # Run the campaign
    results = await app.run_full_campaign(topic, goal)
    
    print("\n" + "=" * 70)
    print("CAMPAIGN RESULTS")
    print("=" * 70)
    
    if results['status'] == 'completed':
        print(f"✅ Campaign completed successfully!")
        print(f"📊 Blueprint ID: {results['blueprint_id']}")
        print(f"📝 Generated {len(results['content_assets'])} content assets")
        print(f"🔍 SEO optimization: {results['seo_results'].get('status', 'N/A')}")
        print(f"📢 Promotion channels: {len(results['promotion_results'].get('channels', []))}")
    else:
        print(f"❌ Campaign failed: {results.get('error', 'Unknown error')}")
    
    print("\nCheck the output/ directory for generated files.")
    print("Check the logs/ directory for detailed execution logs.")


if __name__ == "__main__":
    asyncio.run(main())
