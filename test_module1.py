"""
Test script for Module 1: Market Intelligence Engine
"""
import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.intelligence import MarketIntelligenceEngine
from utils import setup_logging

logger = setup_logging()


async def test_intelligence_engine():
    """Test the Market Intelligence Engine"""
    print("🧠 Testing Market Intelligence Engine")
    print("=" * 50)
    
    # Initialize the engine
    engine = MarketIntelligenceEngine()
    
    # Test topic
    test_topic = "sustainable gardening"
    
    print(f"Analyzing topic: {test_topic}")
    print("-" * 30)
    
    try:
        # Generate blueprint
        blueprint = await engine.generate_blueprint(test_topic)
        
        print("✅ Blueprint generated successfully!")
        print("\n📊 Blueprint Summary:")
        print(f"Title: {blueprint.get('suggested_blog_title', 'N/A')}")
        print(f"Target Audience: {blueprint.get('target_audience', 'N/A')}")
        print(f"Content Angle: {blueprint.get('content_angle', 'N/A')}")
        
        print(f"\n🎯 Key Talking Points ({len(blueprint.get('key_talking_points', []))}):")
        for i, point in enumerate(blueprint.get('key_talking_points', [])[:5], 1):
            print(f"  {i}. {point}")
        
        print(f"\n🔍 SEO Keywords ({len(blueprint.get('seo_keywords', []))}):")
        keywords = blueprint.get('seo_keywords', [])[:10]
        print(f"  {', '.join(keywords)}")
        
        print(f"\n📢 Distribution Channels:")
        channels = blueprint.get('distribution_channels', [])
        for channel in channels:
            print(f"  • {channel}")
        
        print(f"\n💡 Call to Action:")
        print(f"  {blueprint.get('call_to_action', 'N/A')}")
        
        # Show competitor insights if available
        if 'competitor_insights' in blueprint:
            insights = blueprint['competitor_insights']
            print(f"\n🏢 Competitor Insights:")
            if insights.get('common_themes'):
                print(f"  Common themes: {', '.join(insights['common_themes'])}")
        
        # Show community insights if available
        if 'community_insights' in blueprint:
            community = blueprint['community_insights']
            print(f"\n👥 Community Insights:")
            if community.get('questions_to_address'):
                print(f"  Questions to address:")
                for q in community['questions_to_address'][:3]:
                    print(f"    • {q}")
        
        print("\n" + "=" * 50)
        print("✅ Module 1 test completed successfully!")
        
        return blueprint
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        logger.error(f"Intelligence engine test failed: {str(e)}")
        return None


if __name__ == "__main__":
    print("🚀 Prometheus AI - Module 1 Test")
    print("Testing Market Intelligence Engine...")
    print()
    
    # Run the test
    result = asyncio.run(test_intelligence_engine())
    
    if result:
        print("\n📁 Check the 'output' directory for saved blueprint file.")
    else:
        print("\n❌ Test failed. Check logs for details.")
