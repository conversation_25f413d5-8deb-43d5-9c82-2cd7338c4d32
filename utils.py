"""
Utility functions for Prometheus AI
"""
import json
import logging
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from config import settings, LOGS_DIR


def setup_logging():
    """Set up logging configuration"""
    log_file = Path(LOGS_DIR) / f"prometheus_ai_{datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def safe_request_delay():
    """Add a safe delay between requests to avoid rate limiting"""
    time.sleep(settings.REQUEST_DELAY)


def generate_hash(content: str) -> str:
    """Generate a hash for content deduplication"""
    return hashlib.md5(content.encode()).hexdigest()


def save_json(data: Dict, filename: str, directory: str = "output") -> str:
    """Save data as JSON file"""
    filepath = Path(directory) / filename
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    return str(filepath)


def load_json(filepath: str) -> Optional[Dict]:
    """Load JSON file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logging.error(f"Error loading JSON file {filepath}: {e}")
        return None


def clean_text(text: str) -> str:
    """Clean and normalize text content"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = ' '.join(text.split())
    
    # Remove common unwanted characters
    text = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    
    return text.strip()


def extract_keywords(text: str, max_keywords: int = None) -> List[str]:
    """Simple keyword extraction from text"""
    if not text:
        return []
    
    max_keywords = max_keywords or settings.MAX_KEYWORDS
    
    # Simple word frequency approach
    words = text.lower().split()
    word_freq = {}
    
    for word in words:
        # Filter out short words and common stop words
        if len(word) >= settings.MIN_KEYWORD_LENGTH and word not in STOP_WORDS:
            word_freq[word] = word_freq.get(word, 0) + 1
    
    # Sort by frequency and return top keywords
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, freq in sorted_words[:max_keywords]]


def format_timestamp(dt: datetime = None) -> str:
    """Format timestamp for consistent display"""
    if dt is None:
        dt = datetime.now()
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def validate_url(url: str) -> bool:
    """Basic URL validation"""
    return url.startswith(('http://', 'https://'))


def truncate_text(text: str, max_length: int = None) -> str:
    """Truncate text to maximum length"""
    max_length = max_length or settings.MAX_CONTENT_LENGTH
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - 3] + "..."


# Common stop words for keyword extraction
STOP_WORDS = {
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
    'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
    'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
    'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
    'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those',
    'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
}


class ProgressTracker:
    """Simple progress tracking utility"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = time.time()
    
    def update(self, increment: int = 1):
        """Update progress"""
        self.current += increment
        percentage = (self.current / self.total) * 100
        elapsed = time.time() - self.start_time
        
        print(f"\r{self.description}: {self.current}/{self.total} ({percentage:.1f}%) - {elapsed:.1f}s", end="")
        
        if self.current >= self.total:
            print()  # New line when complete
    
    def finish(self):
        """Mark as finished"""
        self.current = self.total
        self.update(0)
