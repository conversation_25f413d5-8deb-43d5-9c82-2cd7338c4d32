"""
Configuration settings for Prometheus AI
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # API Keys - Set these in your .env file
    OPENAI_API_KEY: Optional[str] = Field(None, description="OpenAI API key for LLM")
    ANTHROPIC_API_KEY: Optional[str] = Field(None, description="Anthropic API key for Claude")
    GOOGLE_API_KEY: Optional[str] = Field(None, description="Google API key")
    GOOGLE_CSE_ID: Optional[str] = Field(None, description="Google Custom Search Engine ID")
    
    # Social Media APIs
    TWITTER_API_KEY: Optional[str] = Field(None, description="Twitter API key")
    TWITTER_API_SECRET: Optional[str] = Field(None, description="Twitter API secret")
    TWITTER_ACCESS_TOKEN: Optional[str] = Field(None, description="Twitter access token")
    TWITTER_ACCESS_TOKEN_SECRET: Optional[str] = Field(None, description="Twitter access token secret")
    
    FACEBOOK_ACCESS_TOKEN: Optional[str] = Field(None, description="Facebook access token")
    LINKEDIN_ACCESS_TOKEN: Optional[str] = Field(None, description="LinkedIn access token")
    
    # Email Configuration
    SMTP_SERVER: str = Field("smtp.gmail.com", description="SMTP server")
    SMTP_PORT: int = Field(587, description="SMTP port")
    EMAIL_ADDRESS: Optional[str] = Field(None, description="Email address for sending")
    EMAIL_PASSWORD: Optional[str] = Field(None, description="Email password or app password")
    
    # Database
    DATABASE_URL: str = Field("sqlite:///prometheus_ai.db", description="Database URL")
    
    # Application Settings
    DEBUG: bool = Field(False, description="Debug mode")
    LOG_LEVEL: str = Field("INFO", description="Logging level")
    
    # Web Scraping Settings
    USER_AGENT: str = Field(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        description="User agent for web scraping"
    )
    REQUEST_DELAY: float = Field(1.0, description="Delay between requests in seconds")
    MAX_RETRIES: int = Field(3, description="Maximum number of retries for failed requests")
    
    # Content Generation Settings
    MAX_CONTENT_LENGTH: int = Field(4000, description="Maximum content length for processing")
    DEFAULT_LANGUAGE: str = Field("en", description="Default language for content")
    
    # SEO Settings
    MAX_KEYWORDS: int = Field(10, description="Maximum number of keywords to extract")
    MIN_KEYWORD_LENGTH: int = Field(4, description="Minimum keyword length")
    
    # Analytics Settings
    ANALYTICS_RETENTION_DAYS: int = Field(90, description="Days to retain analytics data")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()


# API Configuration Templates
API_CONFIGS = {
    "openai": {
        "base_url": "https://api.openai.com/v1",
        "model": "gpt-4",
        "max_tokens": 2000,
        "temperature": 0.7
    },
    "anthropic": {
        "base_url": "https://api.anthropic.com",
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 2000,
        "temperature": 0.7
    },
    "google_search": {
        "base_url": "https://www.googleapis.com/customsearch/v1",
        "num_results": 10
    }
}

# Default headers for web requests
DEFAULT_HEADERS = {
    'User-Agent': settings.USER_AGENT,
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
}

# File paths
ASSETS_DIR = "assets"
TEMPLATES_DIR = "templates"
OUTPUT_DIR = "output"
LOGS_DIR = "logs"

# Create directories if they don't exist
for directory in [ASSETS_DIR, TEMPLATES_DIR, OUTPUT_DIR, LOGS_DIR]:
    os.makedirs(directory, exist_ok=True)
