"""
Module 2: Content Generation and Optimization Module
This module generates high-quality, SEO-optimized content from strategy blueprints.
"""
import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from config import settings, OUTPUT_DIR
from utils import setup_logging, save_json, clean_text
from .ai_integration import AIIntegration

logger = setup_logging()


class ContentGenerationModule:
    """Content Generation Module for creating marketing assets"""
    
    def __init__(self):
        self.ai = AIIntegration()
        
    async def generate_content(self, blueprint: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate comprehensive content assets from a strategy blueprint
        
        Args:
            blueprint: Strategy blueprint from Module 1
            
        Returns:
            Dict containing all generated content assets
        """
        logger.info("Generating content assets from blueprint")
        
        try:
            content_assets = {}
            
            # Generate main blog post
            blog_post = await self._generate_blog_post(blueprint)
            if blog_post:
                content_assets['blog_post'] = blog_post
            
            # Generate social media posts
            social_posts = await self._generate_social_posts(blueprint)
            if social_posts:
                content_assets['social_posts'] = social_posts
            
            # Generate email content
            email_content = await self._generate_email_content(blueprint)
            if email_content:
                content_assets['email_content'] = email_content
            
            # Generate featured image
            featured_image = await self._generate_featured_image(blueprint)
            if featured_image:
                content_assets['featured_image'] = featured_image
            
            # Generate meta tags and SEO elements
            seo_elements = await self._generate_seo_elements(blueprint)
            if seo_elements:
                content_assets['seo_elements'] = seo_elements
            
            # Save all assets
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            topic = blueprint.get('suggested_blog_title', 'content').replace(' ', '_').lower()
            
            # Save content assets summary
            assets_filename = f"content_assets_{topic}_{timestamp}.json"
            save_json(content_assets, assets_filename)
            
            logger.info(f"Generated {len(content_assets)} content assets successfully")
            return content_assets
            
        except Exception as e:
            logger.error(f"Content generation failed: {str(e)}")
            raise
    
    async def _generate_blog_post(self, blueprint: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a complete blog post"""
        logger.info("Generating blog post")
        
        title = blueprint.get('suggested_blog_title', 'Untitled Post')
        talking_points = blueprint.get('key_talking_points', [])
        keywords = blueprint.get('seo_keywords', [])
        cta = blueprint.get('call_to_action', 'Learn more about this topic!')
        
        # Create AI prompt for blog post
        prompt = self._create_blog_post_prompt(title, talking_points, keywords, cta)
        
        # Try to get AI-generated content
        ai_content = await self.ai.generate_content(prompt, "blog")
        
        if ai_content:
            html_content = ai_content
        else:
            # Fallback to template-based generation
            html_content = self._generate_fallback_blog_post(title, talking_points, keywords, cta)
        
        # Save HTML file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"blog_post_{title.replace(' ', '_').lower()}_{timestamp}.html"
        filepath = Path(OUTPUT_DIR) / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return {
            'title': title,
            'content': html_content,
            'filename': filename,
            'filepath': str(filepath),
            'word_count': len(html_content.split()),
            'keywords_used': keywords,
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'content_type': 'blog_post',
                'seo_optimized': True
            }
        }
    
    async def _generate_social_posts(self, blueprint: Dict[str, Any]) -> Dict[str, Any]:
        """Generate social media posts for different platforms"""
        logger.info("Generating social media posts")
        
        title = blueprint.get('suggested_blog_title', '')
        keywords = blueprint.get('seo_keywords', [])
        cta = blueprint.get('call_to_action', '')
        
        # Create content summary for social posts
        content_summary = f"New guide on {keywords[0] if keywords else 'this topic'}. {title}"
        
        platforms = ['twitter', 'linkedin', 'facebook', 'instagram']
        
        # Try AI generation first
        ai_posts = await self.ai.generate_social_posts(content_summary, platforms)
        
        if ai_posts:
            social_posts = ai_posts
        else:
            # Fallback generation
            social_posts = self._generate_fallback_social_posts(title, keywords, cta)
        
        return {
            'posts': social_posts,
            'platforms': platforms,
            'content_source': title,
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'content_type': 'social_posts',
                'platforms_count': len(platforms)
            }
        }
    
    async def _generate_email_content(self, blueprint: Dict[str, Any]) -> Dict[str, Any]:
        """Generate email newsletter content"""
        logger.info("Generating email content")
        
        title = blueprint.get('suggested_blog_title', '')
        keywords = blueprint.get('seo_keywords', [])
        cta = blueprint.get('call_to_action', '')
        talking_points = blueprint.get('key_talking_points', [])
        
        # Generate email subject lines
        subject_lines = [
            f"New Guide: {title}",
            f"Everything You Need to Know About {keywords[0] if keywords else 'This Topic'}",
            f"Your Complete {keywords[0] if keywords else 'Guide'} Resource is Here!"
        ]
        
        # Generate email body
        email_body = self._generate_email_body(title, talking_points, cta)
        
        return {
            'subject_lines': subject_lines,
            'body_html': email_body,
            'body_text': self._html_to_text(email_body),
            'cta': cta,
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'content_type': 'email_content',
                'subject_variations': len(subject_lines)
            }
        }
    
    async def _generate_featured_image(self, blueprint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate a featured image for the content"""
        if not PIL_AVAILABLE:
            logger.warning("PIL not available, skipping image generation")
            return None
        
        logger.info("Generating featured image")
        
        title = blueprint.get('suggested_blog_title', 'Featured Content')
        
        try:
            # Image dimensions for social sharing
            width, height = 1200, 630
            
            # Create image
            image = Image.new('RGB', (width, height), color=(26, 13, 219))  # Blue background
            draw = ImageDraw.Draw(image)
            
            # Try to load a font
            try:
                font_size = 60
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
            except (IOError, OSError):
                try:
                    font = ImageFont.load_default()
                    font_size = 40
                except:
                    logger.warning("Could not load font, using basic drawing")
                    font = None
            
            # Text wrapping and positioning
            if font:
                words = title.split()
                lines = []
                current_line = ""
                
                for word in words:
                    test_line = current_line + " " + word if current_line else word
                    if len(test_line) <= 25:  # Character limit per line
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word
                
                if current_line:
                    lines.append(current_line)
                
                # Draw text
                total_height = len(lines) * (font_size + 10)
                y_start = (height - total_height) // 2
                
                for i, line in enumerate(lines):
                    # Get text size
                    bbox = draw.textbbox((0, 0), line, font=font)
                    text_width = bbox[2] - bbox[0]
                    
                    x = (width - text_width) // 2
                    y = y_start + i * (font_size + 10)
                    
                    draw.text((x, y), line, font=font, fill=(255, 255, 255))
            
            # Save image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"featured_image_{title.replace(' ', '_').lower()}_{timestamp}.png"
            filepath = Path(OUTPUT_DIR) / filename
            
            image.save(filepath)
            
            return {
                'filename': filename,
                'filepath': str(filepath),
                'dimensions': f"{width}x{height}",
                'title': title,
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'content_type': 'featured_image',
                    'format': 'PNG'
                }
            }
            
        except Exception as e:
            logger.error(f"Image generation failed: {str(e)}")
            return None
    
    async def _generate_seo_elements(self, blueprint: Dict[str, Any]) -> Dict[str, Any]:
        """Generate SEO meta tags and structured data"""
        logger.info("Generating SEO elements")
        
        title = blueprint.get('suggested_blog_title', '')
        keywords = blueprint.get('seo_keywords', [])
        audience = blueprint.get('target_audience', '')
        
        # Generate meta description
        meta_description = f"Complete guide to {keywords[0] if keywords else 'this topic'}. Perfect for {audience.lower() if audience else 'beginners and experts'}. Learn best practices, tips, and strategies."
        
        # Truncate meta description to 160 characters
        if len(meta_description) > 160:
            meta_description = meta_description[:157] + "..."
        
        # Generate structured data (JSON-LD)
        structured_data = {
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": title,
            "description": meta_description,
            "keywords": ", ".join(keywords[:5]),
            "author": {
                "@type": "Organization",
                "name": "Prometheus AI"
            },
            "datePublished": datetime.now().isoformat(),
            "dateModified": datetime.now().isoformat()
        }
        
        return {
            'meta_title': title,
            'meta_description': meta_description,
            'meta_keywords': ", ".join(keywords[:10]),
            'structured_data': structured_data,
            'og_tags': {
                'og:title': title,
                'og:description': meta_description,
                'og:type': 'article',
                'og:image': 'featured_image.png'
            },
            'twitter_tags': {
                'twitter:card': 'summary_large_image',
                'twitter:title': title,
                'twitter:description': meta_description,
                'twitter:image': 'featured_image.png'
            },
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'content_type': 'seo_elements'
            }
        }
    
    def _create_blog_post_prompt(self, title: str, talking_points: List[str], 
                                keywords: List[str], cta: str) -> str:
        """Create AI prompt for blog post generation"""
        return f"""
        Write a comprehensive, engaging, and SEO-optimized blog post with the following specifications:
        
        Title: {title}
        
        Structure the article around these key talking points:
        {chr(10).join(f"- {point}" for point in talking_points)}
        
        SEO Requirements:
        - Naturally integrate these keywords: {', '.join(keywords[:5])}
        - Use H2 tags for main sections
        - Include at least 1500 words
        - Write in a conversational, helpful tone
        - Include practical examples and actionable advice
        
        End with this call to action: {cta}
        
        Format as complete HTML with proper structure, meta tags, and styling.
        """
    
    def _generate_fallback_blog_post(self, title: str, talking_points: List[str], 
                                   keywords: List[str], cta: str) -> str:
        """Generate fallback blog post when AI is not available"""
        
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="{', '.join(keywords)}">
    <meta name="description" content="Complete guide to {keywords[0] if keywords else 'this topic'}">
    <title>{title}</title>
    <style>
        body {{ 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6; 
            color: #333; 
            max-width: 800px; 
            margin: 2rem auto; 
            padding: 0 1rem; 
        }}
        h1, h2 {{ color: #1a0dab; margin-top: 2rem; }}
        .featured-image {{ max-width: 100%; height: auto; border-radius: 8px; margin: 1rem 0; }}
        .cta {{ 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem; 
            border-radius: 8px; 
            margin: 2rem 0;
            text-align: center;
        }}
        .intro {{ font-size: 1.1em; color: #555; }}
        ul, ol {{ margin: 1rem 0; padding-left: 2rem; }}
        li {{ margin: 0.5rem 0; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    
    <p class="intro">Welcome to our comprehensive guide on {keywords[0] if keywords else 'this important topic'}. 
    Whether you're just getting started or looking to deepen your knowledge, this guide will provide you with 
    practical insights and actionable strategies.</p>
    
    <img src="featured_image.png" alt="{title}" class="featured-image">
"""
        
        # Add content sections based on talking points
        for i, point in enumerate(talking_points, 1):
            html_content += f"""
    <h2>{point}</h2>
    <p>In this section, we'll explore {point.lower()}. This is a crucial aspect that many people overlook, 
    but understanding it will give you a significant advantage in your {keywords[0] if keywords else 'journey'}.</p>
    
    <p>Key considerations for {point.lower()} include:</p>
    <ul>
        <li>Understanding the fundamental principles and best practices</li>
        <li>Common mistakes to avoid and how to prevent them</li>
        <li>Practical tips you can implement immediately</li>
        <li>Advanced strategies for long-term success</li>
    </ul>
    
    <p>By focusing on these elements, you'll be well-positioned to achieve your goals with {keywords[0] if keywords else 'this approach'}.</p>
"""
        
        # Add conclusion and CTA
        html_content += f"""
    <h2>Conclusion</h2>
    <p>We've covered the essential aspects of {keywords[0] if keywords else 'this topic'}, from basic concepts to advanced strategies. 
    The key to success is consistent application of these principles and continuous learning.</p>
    
    <p>Remember, {keywords[0] if keywords else 'this field'} is constantly evolving, so stay curious and keep experimenting 
    with new approaches. The insights shared in this guide will serve as your foundation for continued growth.</p>
    
    <div class="cta">
        <h2>Ready to Take Action?</h2>
        <p>{cta}</p>
    </div>
</body>
</html>"""
        
        return html_content
    
    def _generate_fallback_social_posts(self, title: str, keywords: List[str], cta: str) -> Dict[str, str]:
        """Generate fallback social media posts"""
        primary_keyword = keywords[0] if keywords else "this topic"
        
        return {
            'twitter': f"🚀 New guide: {title[:100]}{'...' if len(title) > 100 else ''}\n\nEverything you need to know about {primary_keyword}!\n\n#{primary_keyword.replace(' ', '')} #guide #tips",
            
            'linkedin': f"📚 Just published: {title}\n\nA comprehensive guide covering:\n• Key strategies and best practices\n• Common pitfalls to avoid\n• Actionable tips for immediate results\n\nPerfect for professionals looking to master {primary_keyword}.\n\n{cta}\n\n#{primary_keyword.replace(' ', '')} #professional #growth",
            
            'facebook': f"🎯 New Resource Alert!\n\n{title}\n\nWe've put together everything you need to know about {primary_keyword} in one comprehensive guide.\n\nWhether you're a beginner or looking to level up your skills, this guide has something for everyone.\n\n{cta}\n\nWhat's your biggest challenge with {primary_keyword}? Let us know in the comments! 👇",
            
            'instagram': f"✨ {title} ✨\n\nYour complete guide to mastering {primary_keyword} is here!\n\nSwipe through our latest post to discover:\n🔹 Essential strategies\n🔹 Expert tips\n🔹 Common mistakes to avoid\n🔹 Action steps for success\n\n{cta}\n\n#{primary_keyword.replace(' ', '')} #guide #tips #success #growth #learning #motivation #inspiration"
        }
    
    def _generate_email_body(self, title: str, talking_points: List[str], cta: str) -> str:
        """Generate email newsletter body"""
        return f"""
<html>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #1a0dab; border-bottom: 2px solid #1a0dab; padding-bottom: 10px;">
            {title}
        </h1>
        
        <p>Hi there!</p>
        
        <p>We're excited to share our latest comprehensive guide with you. This resource covers everything you need to know to get started and succeed.</p>
        
        <h2 style="color: #1a0dab;">What You'll Learn:</h2>
        <ul>
            {chr(10).join(f"<li>{point}</li>" for point in talking_points[:5])}
        </ul>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h3 style="margin-top: 0; color: #1a0dab;">Ready to Get Started?</h3>
            <p>{cta}</p>
            <a href="#" style="background: #1a0dab; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Read the Full Guide
            </a>
        </div>
        
        <p>Best regards,<br>The Prometheus AI Team</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="font-size: 12px; color: #666;">
            You received this email because you subscribed to our newsletter. 
            <a href="#" style="color: #1a0dab;">Unsubscribe</a> | 
            <a href="#" style="color: #1a0dab;">Update Preferences</a>
        </p>
    </div>
</body>
</html>
        """
    
    def _html_to_text(self, html: str) -> str:
        """Convert HTML to plain text"""
        # Simple HTML to text conversion
        import re
        text = re.sub('<[^<]+?>', '', html)
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        return text.strip()
