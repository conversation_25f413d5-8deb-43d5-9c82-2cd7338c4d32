"""
AI Integration Module for Prometheus AI
Handles integration with various AI services (OpenAI, Anthropic, etc.)
"""
import json
import asyncio
from typing import Dict, List, Optional, Any
import requests

from config import settings, API_CONFIGS
from utils import setup_logging

logger = setup_logging()


class AIIntegration:
    """Unified AI integration for various LLM providers"""
    
    def __init__(self):
        self.openai_available = bool(settings.OPENAI_API_KEY)
        self.anthropic_available = bool(settings.ANTHROPIC_API_KEY)
        
        if not (self.openai_available or self.anthropic_available):
            logger.warning("No AI API keys configured. Using fallback responses.")
    
    async def generate_strategy_blueprint(self, prompt: str) -> Optional[Dict[str, Any]]:
        """Generate strategy blueprint using available AI service"""
        try:
            if self.openai_available:
                return await self._call_openai(prompt, response_format="json")
            elif self.anthropic_available:
                return await self._call_anthropic(prompt)
            else:
                logger.warning("No AI service available, using fallback")
                return None
        except Exception as e:
            logger.error(f"AI strategy generation failed: {str(e)}")
            return None
    
    async def generate_content(self, prompt: str, content_type: str = "blog") -> Optional[str]:
        """Generate content using available AI service"""
        try:
            if self.openai_available:
                return await self._call_openai(prompt, response_format="text")
            elif self.anthropic_available:
                return await self._call_anthropic(prompt, response_format="text")
            else:
                logger.warning("No AI service available for content generation")
                return None
        except Exception as e:
            logger.error(f"AI content generation failed: {str(e)}")
            return None
    
    async def analyze_seo(self, content: str, keywords: List[str]) -> Optional[Dict[str, Any]]:
        """Analyze content for SEO optimization"""
        prompt = f"""
        Analyze the following content for SEO optimization:
        
        Target Keywords: {', '.join(keywords)}
        
        Content:
        {content[:2000]}
        
        Provide SEO analysis in JSON format:
        {{
            "keyword_density": {{"keyword": "percentage"}},
            "seo_score": "score out of 100",
            "recommendations": ["list of recommendations"],
            "meta_title": "suggested meta title",
            "meta_description": "suggested meta description"
        }}
        """
        
        try:
            if self.openai_available:
                return await self._call_openai(prompt, response_format="json")
            elif self.anthropic_available:
                return await self._call_anthropic(prompt)
            else:
                return self._fallback_seo_analysis(content, keywords)
        except Exception as e:
            logger.error(f"AI SEO analysis failed: {str(e)}")
            return self._fallback_seo_analysis(content, keywords)
    
    async def generate_social_posts(self, content: str, platforms: List[str]) -> Optional[Dict[str, str]]:
        """Generate social media posts for different platforms"""
        prompt = f"""
        Create social media posts for the following platforms based on this content:
        
        Platforms: {', '.join(platforms)}
        
        Content summary:
        {content[:1000]}
        
        Generate posts in JSON format:
        {{
            "twitter": "Twitter post (280 chars max)",
            "linkedin": "LinkedIn post (professional tone)",
            "facebook": "Facebook post (engaging, casual)",
            "instagram": "Instagram caption with hashtags"
        }}
        """
        
        try:
            if self.openai_available:
                return await self._call_openai(prompt, response_format="json")
            elif self.anthropic_available:
                return await self._call_anthropic(prompt)
            else:
                return self._fallback_social_posts(content, platforms)
        except Exception as e:
            logger.error(f"AI social post generation failed: {str(e)}")
            return self._fallback_social_posts(content, platforms)
    
    async def _call_openai(self, prompt: str, response_format: str = "text") -> Any:
        """Call OpenAI API"""
        if not self.openai_available:
            return None
        
        config = API_CONFIGS["openai"]
        
        headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        messages = [
            {"role": "system", "content": "You are an expert digital marketing strategist and content creator."},
            {"role": "user", "content": prompt}
        ]
        
        if response_format == "json":
            messages[0]["content"] += " Always respond with valid JSON."
        
        data = {
            "model": config["model"],
            "messages": messages,
            "max_tokens": config["max_tokens"],
            "temperature": config["temperature"]
        }
        
        try:
            response = requests.post(
                f"{config['base_url']}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            
            if response_format == "json":
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    logger.error("Invalid JSON response from OpenAI")
                    return None
            else:
                return content
                
        except Exception as e:
            logger.error(f"OpenAI API call failed: {str(e)}")
            return None
    
    async def _call_anthropic(self, prompt: str, response_format: str = "text") -> Any:
        """Call Anthropic Claude API"""
        if not self.anthropic_available:
            return None
        
        config = API_CONFIGS["anthropic"]
        
        headers = {
            "x-api-key": settings.ANTHROPIC_API_KEY,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        system_prompt = "You are an expert digital marketing strategist and content creator."
        if response_format == "json":
            system_prompt += " Always respond with valid JSON."
        
        data = {
            "model": config["model"],
            "max_tokens": config["max_tokens"],
            "temperature": config["temperature"],
            "system": system_prompt,
            "messages": [
                {"role": "user", "content": prompt}
            ]
        }
        
        try:
            response = requests.post(
                f"{config['base_url']}/v1/messages",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            content = result["content"][0]["text"]
            
            if response_format == "json":
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    logger.error("Invalid JSON response from Anthropic")
                    return None
            else:
                return content
                
        except Exception as e:
            logger.error(f"Anthropic API call failed: {str(e)}")
            return None
    
    def _fallback_seo_analysis(self, content: str, keywords: List[str]) -> Dict[str, Any]:
        """Fallback SEO analysis when AI is not available"""
        content_lower = content.lower()
        keyword_density = {}
        
        for keyword in keywords:
            count = content_lower.count(keyword.lower())
            total_words = len(content.split())
            density = (count / total_words * 100) if total_words > 0 else 0
            keyword_density[keyword] = f"{density:.1f}%"
        
        # Simple scoring based on keyword presence
        score = min(100, len([k for k in keywords if k.lower() in content_lower]) * 20)
        
        return {
            "keyword_density": keyword_density,
            "seo_score": str(score),
            "recommendations": [
                "Include target keywords in headings",
                "Add internal and external links",
                "Optimize meta title and description",
                "Use keywords naturally throughout content"
            ],
            "meta_title": f"{keywords[0].title()} - Complete Guide" if keywords else "Complete Guide",
            "meta_description": f"Learn everything about {keywords[0]} in this comprehensive guide." if keywords else "Comprehensive guide with expert insights."
        }
    
    def _fallback_social_posts(self, content: str, platforms: List[str]) -> Dict[str, str]:
        """Fallback social media post generation"""
        # Extract first sentence or create summary
        sentences = content.split('.')
        summary = sentences[0] if sentences else content[:100]
        
        posts = {}
        
        if "twitter" in platforms:
            posts["twitter"] = f"{summary[:200]}... #marketing #tips"
        
        if "linkedin" in platforms:
            posts["linkedin"] = f"Insights on professional growth:\n\n{summary}\n\nWhat are your thoughts? #professional #growth"
        
        if "facebook" in platforms:
            posts["facebook"] = f"Check out this interesting insight:\n\n{summary}\n\nWhat do you think? Let us know in the comments!"
        
        if "instagram" in platforms:
            posts["instagram"] = f"{summary}\n\n#inspiration #tips #growth #success #motivation"
        
        return posts
