"""
Module 1: Market & Audience Intelligence Engine
This module forms the strategic brain of Prometheus AI.
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Any
import requests
from bs4 import BeautifulSoup
from googlesearch import search

from config import settings, DEFAULT_HEADERS, API_CONFIGS
from utils import setup_logging, safe_request_delay, clean_text, extract_keywords, save_json
from .ai_integration import AIIntegration

logger = setup_logging()


class MarketIntelligenceEngine:
    """Market Intelligence Engine for competitor analysis and strategy generation"""
    
    def __init__(self):
        self.ai = AIIntegration()
        self.session = requests.Session()
        self.session.headers.update(DEFAULT_HEADERS)
        
    async def generate_blueprint(self, topic: str) -> Dict[str, Any]:
        """
        Generate a comprehensive marketing strategy blueprint
        
        Args:
            topic: The main topic/keyword to analyze
            
        Returns:
            Dict containing the strategy blueprint
        """
        logger.info(f"Generating intelligence blueprint for: {topic}")
        
        try:
            # Step 1: Perform competitor research
            competitor_data = await self._analyze_competitors(topic)
            
            # Step 2: Analyze community discussions
            community_data = await self._analyze_community_discussions(topic)
            
            # Step 3: Get search trends and keywords
            keyword_data = await self._analyze_keywords(topic)
            
            # Step 4: Synthesize with AI
            blueprint = await self._synthesize_strategy(
                topic, competitor_data, community_data, keyword_data
            )
            
            # Step 5: Save results
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"intelligence_blueprint_{topic.replace(' ', '_')}_{timestamp}.json"
            save_json(blueprint, filename)
            
            logger.info("Intelligence blueprint generated successfully")
            return blueprint
            
        except Exception as e:
            logger.error(f"Failed to generate blueprint: {str(e)}")
            raise
    
    async def _analyze_competitors(self, topic: str) -> Dict[str, Any]:
        """Analyze top competitors for the given topic"""
        logger.info(f"Analyzing competitors for: {topic}")
        
        competitor_queries = [
            f"best {topic} guide",
            f"{topic} tutorial",
            f"how to {topic}",
            f"{topic} tips"
        ]
        
        competitor_data = {
            'urls_analyzed': [],
            'content_themes': [],
            'common_keywords': [],
            'content_gaps': []
        }
        
        for query in competitor_queries:
            try:
                # Get search results
                urls = list(search(query, num_results=3, sleep_interval=2, lang="en"))
                
                for url in urls:
                    if len(competitor_data['urls_analyzed']) >= 10:  # Limit analysis
                        break
                        
                    content = await self._scrape_content(url)
                    if content:
                        competitor_data['urls_analyzed'].append({
                            'url': url,
                            'title': content.get('title', ''),
                            'headings': content.get('headings', []),
                            'keywords': content.get('keywords', []),
                            'content_length': len(content.get('text', ''))
                        })
                        
                        # Extract themes and keywords
                        competitor_data['content_themes'].extend(content.get('headings', []))
                        competitor_data['common_keywords'].extend(content.get('keywords', []))
                
                safe_request_delay()
                
            except Exception as e:
                logger.warning(f"Error analyzing query '{query}': {str(e)}")
                continue
        
        # Process collected data
        competitor_data['common_keywords'] = list(set(competitor_data['common_keywords']))[:20]
        competitor_data['content_themes'] = list(set(competitor_data['content_themes']))[:15]
        
        return competitor_data
    
    async def _analyze_community_discussions(self, topic: str) -> Dict[str, Any]:
        """Analyze community discussions from Reddit and forums"""
        logger.info(f"Analyzing community discussions for: {topic}")
        
        community_queries = [
            f"site:reddit.com {topic} questions",
            f"site:reddit.com {topic} problems",
            f"site:quora.com {topic}",
        ]
        
        community_data = {
            'discussion_urls': [],
            'common_questions': [],
            'pain_points': [],
            'user_interests': []
        }
        
        for query in community_queries:
            try:
                urls = list(search(query, num_results=2, sleep_interval=2, lang="en"))
                
                for url in urls:
                    content = await self._scrape_content(url)
                    if content:
                        community_data['discussion_urls'].append(url)
                        
                        # Extract questions and pain points from text
                        text = content.get('text', '')
                        questions = self._extract_questions(text)
                        community_data['common_questions'].extend(questions)
                
                safe_request_delay()
                
            except Exception as e:
                logger.warning(f"Error analyzing community query '{query}': {str(e)}")
                continue
        
        # Clean up data
        community_data['common_questions'] = list(set(community_data['common_questions']))[:10]
        
        return community_data
    
    async def _analyze_keywords(self, topic: str) -> Dict[str, Any]:
        """Analyze keywords and search trends"""
        logger.info(f"Analyzing keywords for: {topic}")
        
        # For now, we'll generate keyword suggestions based on common patterns
        # In a real implementation, you'd use Google Keyword Planner API or similar
        
        keyword_variations = [
            f"{topic}",
            f"best {topic}",
            f"how to {topic}",
            f"{topic} guide",
            f"{topic} tips",
            f"{topic} tutorial",
            f"{topic} for beginners",
            f"{topic} advanced",
            f"{topic} tools",
            f"{topic} software"
        ]
        
        return {
            'primary_keyword': topic,
            'keyword_variations': keyword_variations,
            'search_volume_estimate': 'medium',  # Placeholder
            'competition_level': 'medium',  # Placeholder
            'related_topics': self._generate_related_topics(topic)
        }
    
    async def _synthesize_strategy(self, topic: str, competitor_data: Dict, 
                                 community_data: Dict, keyword_data: Dict) -> Dict[str, Any]:
        """Use AI to synthesize all data into a strategy blueprint"""
        logger.info("Synthesizing strategy with AI")
        
        # Prepare data for AI analysis
        analysis_data = {
            'topic': topic,
            'competitor_analysis': competitor_data,
            'community_insights': community_data,
            'keyword_research': keyword_data
        }
        
        # Create AI prompt
        prompt = self._create_strategy_prompt(analysis_data)
        
        # Get AI response
        ai_response = await self.ai.generate_strategy_blueprint(prompt)
        
        if ai_response:
            return ai_response
        else:
            # Fallback to rule-based strategy generation
            return self._generate_fallback_strategy(analysis_data)
    
    async def _scrape_content(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape content from a URL"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract title
            title_tag = soup.find('title')
            title = title_tag.get_text().strip() if title_tag else ''
            
            # Extract headings
            headings = [h.get_text().strip() for h in soup.find_all(['h1', 'h2', 'h3'])]
            
            # Extract main text
            paragraphs = [p.get_text().strip() for p in soup.find_all('p')]
            text = ' '.join(paragraphs)
            text = clean_text(text)[:settings.MAX_CONTENT_LENGTH]
            
            # Extract keywords
            keywords = extract_keywords(text)
            
            return {
                'title': title,
                'headings': headings[:10],  # Limit headings
                'text': text,
                'keywords': keywords
            }
            
        except Exception as e:
            logger.warning(f"Failed to scrape {url}: {str(e)}")
            return None
    
    def _extract_questions(self, text: str) -> List[str]:
        """Extract questions from text"""
        sentences = text.split('.')
        questions = []
        
        question_starters = ['what', 'how', 'why', 'when', 'where', 'which', 'who']
        
        for sentence in sentences:
            sentence = sentence.strip().lower()
            if sentence.endswith('?') or any(sentence.startswith(starter) for starter in question_starters):
                if len(sentence) > 10 and len(sentence) < 200:  # Reasonable length
                    questions.append(sentence.capitalize())
        
        return questions[:5]  # Limit to 5 questions
    
    def _generate_related_topics(self, topic: str) -> List[str]:
        """Generate related topics based on the main topic"""
        # Simple rule-based related topic generation
        words = topic.lower().split()
        
        related = []
        if 'marketing' in topic.lower():
            related.extend(['digital marketing', 'content marketing', 'social media marketing'])
        if 'ai' in topic.lower() or 'artificial intelligence' in topic.lower():
            related.extend(['machine learning', 'automation', 'chatbots'])
        if 'business' in topic.lower():
            related.extend(['entrepreneurship', 'startup', 'growth hacking'])
        
        # Add generic related topics
        related.extend([
            f"{topic} tools",
            f"{topic} strategy",
            f"{topic} trends"
        ])
        
        return related[:8]
    
    def _create_strategy_prompt(self, analysis_data: Dict) -> str:
        """Create AI prompt for strategy generation"""
        return f"""
        Based on the following market research data, create a comprehensive digital marketing strategy blueprint.
        
        Topic: {analysis_data['topic']}
        
        Competitor Analysis:
        - URLs analyzed: {len(analysis_data['competitor_analysis']['urls_analyzed'])}
        - Common themes: {', '.join(analysis_data['competitor_analysis']['content_themes'][:5])}
        - Top keywords: {', '.join(analysis_data['competitor_analysis']['common_keywords'][:10])}
        
        Community Insights:
        - Common questions: {', '.join(analysis_data['community_insights']['common_questions'][:5])}
        
        Keyword Research:
        - Primary keyword: {analysis_data['keyword_research']['primary_keyword']}
        - Related keywords: {', '.join(analysis_data['keyword_research']['keyword_variations'][:5])}
        
        Generate a JSON strategy blueprint with the following structure:
        {{
            "suggested_blog_title": "SEO-optimized blog post title",
            "target_audience": "Description of target audience",
            "key_talking_points": ["List of 5-7 key points to cover"],
            "seo_keywords": ["List of 10 primary and secondary keywords"],
            "content_angle": "Unique angle or approach",
            "call_to_action": "Suggested call to action",
            "content_format": "Recommended content format",
            "distribution_channels": ["List of recommended channels"]
        }}
        """
    
    def _generate_fallback_strategy(self, analysis_data: Dict) -> Dict[str, Any]:
        """Generate fallback strategy when AI is not available"""
        topic = analysis_data['topic']
        competitor_data = analysis_data['competitor_analysis']
        community_data = analysis_data['community_insights']
        keyword_data = analysis_data['keyword_research']
        
        return {
            "suggested_blog_title": f"The Complete Guide to {topic.title()} in 2024",
            "target_audience": f"Beginners and intermediates interested in {topic}",
            "key_talking_points": [
                f"What is {topic} and why is it important?",
                f"Getting started with {topic}",
                f"Common {topic} mistakes to avoid",
                f"Best practices for {topic}",
                f"Tools and resources for {topic}",
                f"Future trends in {topic}"
            ],
            "seo_keywords": keyword_data['keyword_variations'][:10],
            "content_angle": "Comprehensive beginner-friendly guide",
            "call_to_action": f"Download our free {topic} checklist",
            "content_format": "Long-form blog post with infographics",
            "distribution_channels": ["Blog", "Social Media", "Email Newsletter", "SEO"],
            "competitor_insights": {
                "content_gaps": competitor_data.get('content_gaps', []),
                "common_themes": competitor_data.get('content_themes', [])[:5]
            },
            "community_insights": {
                "questions_to_address": community_data.get('common_questions', [])[:5]
            }
        }
